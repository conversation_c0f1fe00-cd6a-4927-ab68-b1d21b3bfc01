{"common": {"required": "Campos obligatorios", "submit": "Enviar", "submitting": "Enviando...", "success": "¡Éxito!", "error": "Error", "warning": "Advertencia", "loading": "Cargando...", "select_placeholder": "Por favor seleccione...", "language": "Idioma", "optional": "opcional", "yes": "Sí", "no": "No", "remove": "Eliminar"}, "navigation": {"home": "<PERSON><PERSON>o", "privacy_policy": "Política de Privacidad", "terms_of_service": "Términos de Servicio", "toggle_hiring": "<PERSON><PERSON>r", "toggle_job_seeking": "Buscar Trabajo", "toggle_hiring_short": "<PERSON><PERSON>r", "toggle_job_seeking_short": "Trabajos"}, "hero": {"title": "Construido para trabajadores.", "title_powered": "Impulsado por IA.", "subtitle": "Omite el papeleo. Consigue ese trabajo.", "subtitle_2": "<PERSON><PERSON><PERSON><PERSON>, simple y en tu idioma.", "cta_worker": "Únet<PERSON> G<PERSON>", "cta_company": "<PERSON><PERSON><PERSON>", "trust_indicator": "2,500+ trabajadores"}, "features": {"title": "¿Por qué elegir Sohnus?", "subtitle": "Hacemos que la búsqueda de trabajo sea simple y efectiva", "feature1_title": "<PERSON>ú hablas, nosotros hacemos el resto", "feature1_description": "<PERSON>hnus escucha tu voz, entiende en qué eres bueno y crea tu propio perfil.", "feature2_title": "Coincide con los trabajos correctos", "feature2_description": "No necesitas buscar durante horas. Nuestra IA encuentra los mejores trabajos coincidentes e incluso aplica por ti.", "feature3_title": "Un futuro mejor", "feature3_description": "Con más experiencia, te mostramos trabajos mejor pagados, horarios más estables y roles con más responsabilidad."}, "forms": {"title": "Comienza Hoy", "subtitle": "Elige la opción correcta para ti: ¿Estás buscando trabajo o eres una empresa que busca profesionales calificados?", "tab_worker": "Estoy buscando trabajo", "tab_company": "Estamos contratando", "required_note": "Campos obligatorios", "footer_agreement": "Al enviar este formulario, aceptas nuestra", "and": "y"}, "worker_form": {"title": "Solicitud de Buscador de Empleo", "subtitle": "Completa los siguientes campos. Crearemos automáticamente tu currículum con esta información.", "success_title": "¡Solicitud enviada exitosamente!", "success_description": "Te contactaremos pronto. También recibirás un email de confirmación en breve.", "personal_info": "Información Personal", "job_expectations": "Expectativas Laborales", "work_history": "Historial Laboral", "skills_qualifications": "Habilidades y Calificaciones", "fields": {"first_name": "Nombre", "last_name": "Apellido", "email": "Correo Electrónico", "phone": "Número de Teléfono", "city": "Ciudad Actual", "nationality": "Nacionalidad", "avatar": "Foto de Perfil", "availability_date": "Fecha de Disponibilidad", "willing_to_travel": "Disposición a Viajar", "salary_expectation": "Expectativa Salarial (€/mes)", "desired_position": "Posición Deseada", "employment_type": "Tipo de Empleo", "job_title": "Título del Trabajo", "job_company": "Empresa", "job_start": "Fecha de Inicio", "job_end": "<PERSON><PERSON>", "job_duration": "Duración", "job_tasks": "Tareas y Responsabilidades Principales", "job1_title": "Posición Más Reciente", "job1_company": "Empresa Más Reciente", "job1_duration": "Duración", "job1_tasks": "Tareas y Responsabilidades Principales", "education_level": "<PERSON>vel <PERSON>", "education_start": "Fecha de Inicio", "education_end": "<PERSON><PERSON>", "education_school": "Escuela/Universidad", "education_detail": "Detalles del Título/Programa", "german_level": "<PERSON><PERSON>", "other_languages": "<PERSON><PERSON><PERSON>", "driving_license": "Licencia de Conducir", "skills_certs": "Habilidades y Certificados Adicionales"}, "job_entry": "Experiencia Laboral", "add_job": "Agregar <PERSON>aj<PERSON>", "education_entry": "Educación", "add_education": "Agregar Educación", "education_details": "Detalles de Educación", "avatar_help": "Subir foto profesional (máx. 2MB, JPG/PNG)", "avatar_required": "La foto de perfil es obligatoria", "avatar_invalid_format": "Por favor sube un archivo de imagen válido (JPG, PNG)", "avatar_too_large": "El archivo de imagen debe ser menor a 2MB", "avatar_too_small": "El archivo de imagen es demasiado pequeño. Por favor sube una imagen válida", "placeholders": {"first_name": "Ingresa tu nombre", "last_name": "Ingresa tu apellido", "email": "<EMAIL>", "phone": "+49 **********", "city": "ej. <PERSON>", "nationality": "ej. <PERSON><PERSON>, China, Polaca", "desired_position": "ej. <PERSON><PERSON><PERSON><PERSON>", "employment_type": "Selecciona tipo de empleo", "job1_title": "ej. <PERSON><PERSON><PERSON><PERSON> Pedidos", "job1_company": "ej. Amazon Logistics", "job1_duration": "ej. 2 años", "job1_tasks": "Describe tus responsabilidades principales...", "education_level": "Selecciona tu nivel educativo", "german_level": "Selecciona tu nivel de alemán", "other_languages": "ej. <PERSON><PERSON><PERSON><PERSON> (C1), <PERSON><PERSON><PERSON><PERSON> (A2)", "driving_license": "Selecciona tu tipo de licencia", "skills_certs": "ej. Cert<PERSON><PERSON> Soldadura, Certificado SCC, MS Office..."}, "options": {"employment_type": {"full_time": "Tiempo Completo", "part_time": "Tiempo Parcial", "contract": "Contrato", "temporary": "Temporal"}, "education_level": {"no_degree": "Sin título", "vocational_training": "Formación profesional", "high_school": "Bachillerato", "bachelors_degree": "Licenciatura", "masters_degree": "Maestría", "phd": "<PERSON><PERSON>"}, "german_level": {"a1": "A1 (Principiante)", "a2": "A2 (Elemental)", "b1": "B1 (Intermedio)", "b2": "B2 (Intermedio Alto)", "c1": "C1 (<PERSON><PERSON><PERSON>)", "c2": "C2 (Competente)", "native_speaker": "Hablante Nativo"}, "driving_license": {"none": "Sin licencia", "class_b": "Clase B (Automóvil)", "class_c1": "Clase C1 (Camión Pequeño)", "class_ce": "Clase CE (Camión con Remolque)", "forklift_license": "Licencia de Montacargas"}}}, "team": {"title": "Conoce Nuestro Equipo", "subtitle": "Somos un grupo diverso de innovadores, ingenieros y expertos en carreras profesionales dedicados a revolucionar cómo las personas encuentran trabajo significativo.", "hero_title": "Innovación a Través de la Colaboración", "hero_subtitle": "Nuestro equipo trabaja junto para construir el futuro del trabajo, una conexión a la vez.", "members": {"roger": {"name": "<PERSON>", "role": "CEO y Fundador", "bio": "Con experiencia en ingeniería y gestión, <PERSON> construyendo Sohnus para hacer que la búsqueda de empleo sea más rápida, justa y fácil—usando IA para conectar trabajadores y empresas sin barreras de idioma."}, "bijun": {"name": "<PERSON><PERSON><PERSON> <PERSON><PERSON><PERSON>", "role": "CTO y Cofundadora", "bio": "Con un doctorado en Ingeniería y amplia experiencia en sistemas avanzados, Bijun lidera la visión técnica de Sohnus. Construye y escala la plataforma de IA que impulsa la búsqueda automática de empleo—eliminando brechas de idioma y conectando trabajadores y empleadores eficientemente."}, "yuanwei": {"name": "<PERSON><PERSON> Fang", "role": "CPO y Cofundadora", "bio": "Yuanwei combina profunda experiencia en UX con experiencia práctica en crear experiencias de chat con IA. Lidera el diseño técnico de Sohnus, asegurando que nuestra interfaz de búsqueda de empleo sea simple, útil—y construida para usuarios reales."}}}, "footer": {"company_description": "Haciendo el empleo significativo accesible para cada trabajador a través de búsqueda de empleo impulsada por IA.", "job_seekers": "Para Buscadores de Empleo", "employers": "Para Empleadores", "contact_info": "Información de Contacto", "legal": "Legal", "apply_now": "Aplica<PERSON>", "post_jobs": "Publicar Empleos", "privacy_policy": "Política de Privacidad", "terms_of_service": "Términos de Servicio", "copyright": "© 2025 Sohnus. Todos los derechos reservados."}, "company_form": {"title": "Consulta de Empresa", "subtitle": "Cuéntanos sobre tu empresa y requisitos de personal", "success_title": "¡Consulta enviada exitosamente!", "success_description": "Te contactaremos pronto. También recibirás un email de confirmación en breve.", "fields": {"company_name": "Nombre de la Empresa", "contact_person": "Persona de Contacto", "contact_email": "<PERSON><PERSON>", "contact_phone": "Teléfono de Contacto", "company_website": "Sitio Web de la Empresa", "needed_positions": "Posiciones Necesarias", "number_of_vacancies": "Número de Vacantes", "work_location": "Ubicación de Trabajo", "required_skills": "Habilidades Requeridas", "employment_model": "<PERSON><PERSON>", "urgency": "Urgencia", "job_description": "Descripción del Trabajo"}, "placeholders": {"company_name": "Nombre de tu empresa", "contact_person": "Nombre completo de la persona de contacto", "contact_email": "<EMAIL>", "contact_phone": "+49 **********", "company_website": "https://www.empresa.com", "needed_positions": "ej. <PERSON><PERSON><PERSON><PERSON>, Conductor de Mont<PERSON>rgas", "number_of_vacancies": "ej. 5", "work_location": "ej<PERSON> <PERSON><PERSON>, <PERSON><PERSON><PERSON>", "required_skills": "ej. <PERSON><PERSON><PERSON> de montacargas, Alemán nivel B2, Condición física", "employment_model": "Selecciona tipo de empleo", "urgency": "Selecciona nivel de urgencia", "job_description": "Descripción detallada de las responsabilidades del trabajo, ambiente laboral, beneficios y otra información relevante..."}, "options": {"employment_model": {"full_time": "Tiempo Completo", "part_time": "Tiempo Parcial", "contract": "Contrato", "temporary": "Temporal", "internship": "Prácticas"}, "urgency": {"immediate": "Inmediato (dentro de 1 semana)", "urgent": "Urgente (dentro de 2-4 semanas)", "normal": "Normal (dentro de 1-2 meses)", "flexible": "Flexible (cuando se encuentre el candidato adecuado)"}}}}