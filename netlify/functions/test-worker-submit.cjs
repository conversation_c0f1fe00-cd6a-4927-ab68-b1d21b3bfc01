exports.handler = async (event) => {
  const headers = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Headers': 'Content-Type',
    'Access-Control-Allow-Methods': 'GET, OPTIONS'
  };

  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers, body: '' };
  }

  try {
    // Test the exact same logic as submit-form but with detailed logging
    const nocoDBUrl = process.env.NOCODB_URL;
    const nocoDBToken = process.env.NOCODB_TOKEN;
    const brevoApiKey = process.env.BREVO_API_KEY;
    const notificationEmail = process.env.NOTIFICATION_EMAIL;

    console.log('Environment variables check:', {
      hasNocoDBUrl: !!nocoDBUrl,
      hasNocoDBToken: !!nocoDBToken,
      hasBrevoApiKey: !!brevoApiKey,
      hasNotificationEmail: !!notificationEmail,
      nocoDBUrl: nocoDBUrl,
      notificationEmail: notificationEmail
    });

    // Check if environment variables are set
    if (!nocoDBUrl || !nocoDBToken) {
      return {
        statusCode: 500,
        headers,
        body: JSON.stringify({
          error: 'Missing NocoDB environment variables',
          details: {
            hasNocoDBUrl: !!nocoDBUrl,
            hasNocoDBToken: !!nocoDBToken,
            hasBrevoApiKey: !!brevoApiKey,
            hasNotificationEmail: !!notificationEmail
          }
        })
      };
    }

    // Create test worker form data exactly like the frontend sends (Chinese version)
    const testFormData = {
      FirstName: "明",
      LastName: "李",
      Email: "<EMAIL>",
      PhoneNumber: "+49 **********",
      City: "柏林",
      Nationality: "中国",
      DesiredPosition: "软件开发工程师",
      EmploymentType: "full_time",
      Job1_Title: "前端开发工程师",
      Job1_Company: "科技有限公司",
      Job1_Duration: "2年",
      Job1_Tasks: "使用React和Node.js开发网络应用程序",
      EducationLevel: "bachelors_degree",
      GermanLevel: "b2",
      OtherLanguages: "英语 (C1), 西班牙语 (A2)",
      DrivingLicense: "class_b",
      SkillsAndCerts: "React, Node.js, TypeScript, AWS",
      Language: "zh",
      formType: "worker"
    };

    console.log('Test form data:', testFormData);

    // Test NocoDB submission
    const formType = testFormData.formType;
    const nocoTableId = formType === 'worker' ? 'mcv0hz2nurqap37' : 'm2yux6be57tujg8';
    
    const baseUrl = nocoDBUrl.endsWith('/') ? nocoDBUrl.slice(0, -1) : nocoDBUrl;
    const nocoApiUrl = `${baseUrl}/api/v2/tables/${nocoTableId}/records`;

    console.log('NocoDB submission details:', {
      formType,
      nocoTableId,
      baseUrl,
      nocoApiUrl
    });

    let nocoResult = null;
    try {
      const nocoResponse = await fetch(nocoApiUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'xc-token': nocoDBToken,
        },
        body: JSON.stringify(testFormData),
      });

      const nocoResponseText = await nocoResponse.text();
      
      nocoResult = {
        success: nocoResponse.ok,
        status: nocoResponse.status,
        statusText: nocoResponse.statusText,
        response: nocoResponseText,
        headers: Object.fromEntries(nocoResponse.headers.entries())
      };

      console.log('NocoDB response:', nocoResult);

      if (!nocoResponse.ok) {
        console.error('NocoDB Error Details:', {
          status: nocoResponse.status,
          statusText: nocoResponse.statusText,
          response: nocoResponseText,
          url: nocoApiUrl
        });
      }
    } catch (nocoError) {
      nocoResult = {
        success: false,
        error: nocoError.message,
        stack: nocoError.stack
      };
      console.error('NocoDB fetch error:', nocoError);
    }

    // Test Brevo email if NocoDB succeeded and Brevo is configured
    let brevoResult = null;
    if (nocoResult.success && brevoApiKey && notificationEmail) {
      try {
        const emailSubject = `New worker form: ${testFormData.FirstName} ${testFormData.LastName}`;
        const emailHtmlContent = `<p>A new form has been submitted via the WORKER form.</p><pre>${JSON.stringify(testFormData, null, 2)}</pre>`;

        const brevoResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json',
            'api-key': brevoApiKey,
          },
          body: JSON.stringify({
            sender: { email: '<EMAIL>', name: 'Sohnus Form' },
            to: [{ email: notificationEmail }],
            subject: emailSubject,
            htmlContent: emailHtmlContent,
          }),
        });

        const brevoResponseText = await brevoResponse.text();
        
        brevoResult = {
          success: brevoResponse.ok,
          status: brevoResponse.status,
          statusText: brevoResponse.statusText,
          response: brevoResponseText
        };

        console.log('Brevo response:', brevoResult);
      } catch (brevoError) {
        brevoResult = {
          success: false,
          error: brevoError.message,
          stack: brevoError.stack
        };
        console.error('Brevo error:', brevoError);
      }
    }

    // Test acknowledgment email functionality
    let ackEmailResult = null;
    if (nocoResult.success && brevoApiKey) {
      try {
        const templateMap = {
          worker: {
            de: 1,
            en: 2,
            zh: 4,
            es: 3,
          },
          company: {
            de: 5,
            en: 8,
            zh: 7,
            es: 6,
          }
        };

        const language = testFormData.Language || testFormData.language || 'en';
        const templateId = templateMap[testFormData.formType]?.[language];

        console.log('Acknowledgment email test:', {
          language,
          formType: testFormData.formType,
          templateId,
          userEmail: testFormData.Email,
          availableTemplates: templateMap[testFormData.formType]
        });

        if (templateId) {
          const dynamicParams = testFormData.formType === 'worker'
            ? { FirstName: testFormData.FirstName }
            : { CompanyName: testFormData.CompanyName };

          const ackEmailResponse = await fetch('https://api.brevo.com/v3/smtp/email', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json',
              'api-key': brevoApiKey,
            },
            body: JSON.stringify({
              to: [{ email: testFormData.Email }],
              templateId: templateId,
              params: dynamicParams,
            }),
          });

          const ackEmailResponseText = await ackEmailResponse.text();

          ackEmailResult = {
            success: ackEmailResponse.ok,
            status: ackEmailResponse.status,
            statusText: ackEmailResponse.statusText,
            response: ackEmailResponseText,
            templateId,
            language,
            dynamicParams
          };

          console.log('Acknowledgment email response:', ackEmailResult);
        } else {
          ackEmailResult = {
            success: false,
            error: 'No template found for language/form type combination',
            language,
            formType: testFormData.formType,
            availableTemplates: templateMap[testFormData.formType]
          };
        }
      } catch (ackEmailError) {
        ackEmailResult = {
          success: false,
          error: ackEmailError.message,
          stack: ackEmailError.stack
        };
        console.error('Acknowledgment email error:', ackEmailError);
      }
    }

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Worker form submission test completed',
        timestamp: new Date().toISOString(),
        testData: testFormData,
        results: {
          nocodb: nocoResult,
          brevo: brevoResult,
          acknowledgmentEmail: ackEmailResult
        },
        environment: {
          hasNocoDBUrl: !!nocoDBUrl,
          hasNocoDBToken: !!nocoDBToken,
          hasBrevoApiKey: !!brevoApiKey,
          hasNotificationEmail: !!notificationEmail,
          nocoApiUrl
        }
      })
    };

  } catch (error) {
    console.error('Test error:', error);
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
