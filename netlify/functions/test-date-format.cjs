// Test function for date format conversion
const headers = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'Content-Type',
  'Access-Control-Allow-Methods': 'POST, OPTIONS',
};

// Test date conversion functions
const formatDateForInput = (dateStr) => {
  if (!dateStr) return "";
  if (dateStr.includes("-")) return dateStr; // Already in yyyy-mm-dd format
  
  const parts = dateStr.split("/");
  if (parts.length === 3) {
    const [day, month, year] = parts;
    return `${year}-${month.padStart(2, '0')}-${day.padStart(2, '0')}`;
  }
  return dateStr;
};

const formatDateForStorage = (dateStr) => {
  if (!dateStr) return "";
  if (dateStr.includes("/")) return dateStr; // Already in dd/mm/yyyy format
  
  const parts = dateStr.split("-");
  if (parts.length === 3) {
    const [year, month, day] = parts;
    return `${day}/${month}/${year}`;
  }
  return dateStr;
};

const calculateDuration = (startDate, endDate) => {
  if (!startDate || !endDate) return "";
  
  // Convert dd/mm/yyyy to Date object
  const parseDate = (dateStr) => {
    if (dateStr.includes("/")) {
      const [day, month, year] = dateStr.split("/");
      return new Date(parseInt(year), parseInt(month) - 1, parseInt(day));
    } else if (dateStr.includes("-")) {
      return new Date(dateStr);
    }
    return new Date(dateStr);
  };
  
  const start = parseDate(startDate);
  const end = parseDate(endDate);
  
  if (start >= end) return "";
  
  let years = end.getFullYear() - start.getFullYear();
  let months = end.getMonth() - start.getMonth();
  
  if (months < 0) {
    years--;
    months += 12;
  }
  
  const parts = [];
  if (years > 0) {
    parts.push(`${years} ${years === 1 ? 'Year' : 'Years'}`);
  }
  if (months > 0) {
    parts.push(`${months} ${months === 1 ? 'Month' : 'Months'}`);
  }
  
  return parts.length > 0 ? parts.join(' ') : "";
};

exports.handler = async (event) => {
  // Handle CORS preflight
  if (event.httpMethod === 'OPTIONS') {
    return { statusCode: 200, headers };
  }

  // Only accept POST requests
  if (event.httpMethod !== 'POST') {
    return {
      statusCode: 405,
      headers,
      body: JSON.stringify({ error: 'Method Not Allowed' })
    };
  }

  try {
    console.log('Date format test started');

    // Test date conversions
    const testCases = [
      {
        description: "Convert dd/mm/yyyy to yyyy-mm-dd for input",
        input: "15/01/2024",
        expected: "2024-01-15",
        actual: formatDateForInput("15/01/2024")
      },
      {
        description: "Convert yyyy-mm-dd to dd/mm/yyyy for storage",
        input: "2024-01-15",
        expected: "15/01/2024",
        actual: formatDateForStorage("2024-01-15")
      },
      {
        description: "Calculate duration from dd/mm/yyyy dates",
        input: { start: "15/01/2022", end: "15/01/2024" },
        expected: "2 Years",
        actual: calculateDuration("15/01/2022", "15/01/2024")
      },
      {
        description: "Calculate duration with months",
        input: { start: "01/06/2021", end: "01/12/2021" },
        expected: "6 Months",
        actual: calculateDuration("01/06/2021", "01/12/2021")
      },
      {
        description: "Calculate duration with years and months",
        input: { start: "01/09/2018", end: "30/06/2022" },
        expected: "3 Years 9 Months",
        actual: calculateDuration("01/09/2018", "30/06/2022")
      }
    ];

    const results = testCases.map(testCase => ({
      ...testCase,
      passed: testCase.actual === testCase.expected
    }));

    const allPassed = results.every(result => result.passed);

    // Test checkbox value conversion
    const checkboxTests = [
      {
        description: "Checkbox checked should be 'yes'",
        input: true,
        expected: "yes",
        actual: true ? "yes" : "no"
      },
      {
        description: "Checkbox unchecked should be 'no'",
        input: false,
        expected: "no",
        actual: false ? "yes" : "no"
      }
    ];

    const checkboxResults = checkboxTests.map(testCase => ({
      ...testCase,
      passed: testCase.actual === testCase.expected
    }));

    const allCheckboxPassed = checkboxResults.every(result => result.passed);

    return {
      statusCode: 200,
      headers,
      body: JSON.stringify({
        message: 'Date format test completed',
        timestamp: new Date().toISOString(),
        dateTests: {
          allPassed,
          results
        },
        checkboxTests: {
          allPassed: allCheckboxPassed,
          results: checkboxResults
        },
        summary: {
          totalTests: results.length + checkboxResults.length,
          passed: results.filter(r => r.passed).length + checkboxResults.filter(r => r.passed).length,
          failed: results.filter(r => !r.passed).length + checkboxResults.filter(r => !r.passed).length
        }
      })
    };

  } catch (error) {
    console.error('Test error:', error);
    
    return {
      statusCode: 500,
      headers,
      body: JSON.stringify({
        error: 'Test failed',
        details: error.message,
        stack: error.stack,
        timestamp: new Date().toISOString()
      })
    };
  }
};
